<script lang="tsx" setup>
import { ref } from "vue";

import CloudImport from "./components/tab/CloudImport.vue";
import Home from "./components/tab/Home/index.vue";
import Knowledge from "./components/tab/Knowledge.vue";
import LinkPaste from "./components/tab/LinkPaste.vue";
import Local from "./components/tab/Local.vue";
import PPTSelect from "./components/tab/PPTSelect/index.vue";
import PPTUpload from "./components/tab/PPTUpload.vue";
import TextPaste from "./components/tab/TextPaste/index.vue";

const currentComponent = ref("textPaste");
const setCurrentComponent = (component: string) => {
  currentComponent.value = component;
};

// 组件映射表
const componentMap: Record<string, any> = {
  home: Home,
  cloud: CloudImport,
  knowledge: Knowledge,
  local: Local,
  linkPaste: LinkPaste,
  textPaste: TextPaste,
  pptSelect: PPTSelect,
  pptUpload: PPTUpload,
};

defineExpose({
  currentComponent,
  setCurrentComponent,
});
</script>

<template>
  <div class="h-full">
    <component
      :is="componentMap[currentComponent]"
      :currentComponent="currentComponent"
      :setCurrentComponent="setCurrentComponent"
    />
  </div>
</template>
