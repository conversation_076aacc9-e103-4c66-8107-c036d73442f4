<script lang="ts" setup>
import { SvgIcon } from "@tg-fe/ui";

interface Props {
  class?: string;
  disabled?: boolean;
  tooltip?: {
    content: string;
    disabled: boolean;
  };
  onClick?: () => void;
}

const { tooltip, class: className, disabled, onClick } = defineProps<Props>();

const handleAdd = () => {
  if (onClick && !disabled) {
    onClick();
  }
};
</script>

<template>
  <div
    :class="[
      'flex',
      {
        'cursor-not-allowed opacity-50': disabled,
        'cursor-pointer': !disabled,
      },
      className,
    ]"
  >
    <ElTooltip :content="tooltip?.content" :disabled="tooltip?.disabled">
      <div
        class="bg-fill-fill-4 text-text-icon-text-5 fill-text-icon-text-5 flex h-[32px] select-none items-center justify-center gap-[6px] rounded-[10px] px-3"
        @click="handleAdd"
      >
        <SvgIcon class="h-[16px] w-[16px]" name="ic_add"></SvgIcon>
        <div><slot></slot></div>
      </div>
    </ElTooltip>
  </div>
</template>
