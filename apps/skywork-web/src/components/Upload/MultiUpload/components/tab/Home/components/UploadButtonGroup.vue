<script lang="ts" setup>
import { defineProps } from "vue";
import ButtonGroup from "@/components/Upload/MultiUpload/components/other/ButtonGroup.vue";

interface Props {
  setCurrentComponent: (component: string) => void;
}
const { setCurrentComponent } = defineProps<Props>();
</script>
<template>
  <div class="mb-6 flex items-center gap-4">
    <ButtonGroup
      :className="{
        outer: 'bg-text-icon-text-1',
        item: 'text-text-icon-text-6 fill-text-icon-text-6',
      }"
      :list="[{ name: '本地上传', iconName: 'ic_upload', onClick: () => setCurrentComponent('local') }]"
    />
    <ButtonGroup
      :className="{
        outer: 'bg-fill-fill-3-hover',
        item: 'text-text-icon-text-2 fill-text-icon-text-2',
      }"
      :list="[
        {
          name: '添加模版',
          iconName: 'ic_add',
          children: [
            { name: '上传模版', iconName: 'ic_add', onClick: () => setCurrentComponent('pptUpload') },
            { name: '选择模版', iconName: 'document-forward', onClick: () => setCurrentComponent('pptSelect') },
          ],
        },
        { name: '知识库', iconName: 'ic_knowledge base', onClick: () => setCurrentComponent('knowledge') },
        {
          name: '粘贴',
          iconName: 'document-copy',
          children: [
            { name: '粘贴链接', iconName: 'link', onClick: () => setCurrentComponent('linkPaste') },
            { name: '粘贴文本', iconName: 'text-block', onClick: () => setCurrentComponent('textPaste') },
          ],
        },
        {
          name: '粘贴2',
          iconName: 'document-copy',
          children: [
            { name: '粘贴链接2', iconName: 'link', onClick: () => setCurrentComponent('linkPaste') },
            { name: '粘贴文本2', iconName: 'text-block', onClick: () => setCurrentComponent('textPaste') },
          ],
        },
        { name: '从云盘导入', iconName: 'share', onClick: () => setCurrentComponent('cloud') },
      ]"
    />
  </div>
</template>
