<script lang="ts" setup>
import { DocInput, SvgIcon } from "@tg-fe/ui";
import { ref } from "vue";
import UploadButton from "../other/UploadButton.vue";

const list = ref<(string | undefined)[]>([undefined]);

const handleAddLink = () => {
  list.value.push(undefined);
};
</script>

<template>
  <div class="h-[428px] space-y-3 overflow-y-auto">
    <div class="text-text-icon-text-3 text-xs">请在此处粘贴链接</div>
    <DocInput v-for="item in list" :key="item" type="textarea" placeholder="粘贴链接" />

    <UploadButton @click="handleAddLink">添加链接</UploadButton>
  </div>
</template>
