<script lang="ts" setup>
import { computed } from "vue";
import { SvgIcon } from "@tg-fe/ui";
import { COMPONENT_MAP } from "../../MultiUpload/constants";
import { ElTooltip } from "element-plus";
import { DEFAULT_TAB, isDefaultTab } from "@/components/Upload/MultiUpload/utils/isDefaultTab";

interface Props {
  currentComponent: string;
  handleClose: () => void;
  handleSetCurrentComponent: (component: string) => void;
}

const props = defineProps<Props>();
const componentConfig = computed(() => {
  return COMPONENT_MAP[props.currentComponent];
});

const handleBack = () => {
  props.handleSetCurrentComponent(DEFAULT_TAB);
};
</script>
<template>
  <div class="mb-2 flex items-center justify-between">
    <div class="flex items-center">
      <SvgIcon
        class="mr-[8px] h-5 w-5 cursor-pointer fill-[--text-icon-text-2]"
        v-if="!isDefaultTab(props.currentComponent)"
        name="ic_drop down menu_left"
        @click="handleBack"
      />
      <div class="text-[20px]/[150%] font-medium">{{ componentConfig.title }}</div>
      <ElTooltip v-if="componentConfig.tips" placement="right" effect="dark" :content="componentConfig.tips">
        <SvgIcon class="ml-3 h-[18px] w-[18px] fill-[--text-icon-text-3]" name="ic_tip" />
      </ElTooltip>
    </div>
    <SvgIcon class="h-6 w-6 cursor-pointer fill-[--text-icon-text-4]" name="ic_close2" @click="props.handleClose" />
  </div>
</template>
